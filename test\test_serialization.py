"""
Test solution for verifying custom class serialization formats.
"""

class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next
    
    def __eq__(self, other):
        if not isinstance(other, ListNode):
            return False
        current_self = self
        current_other = other
        while current_self and current_other:
            if current_self.val != current_other.val:
                return False
            current_self = current_self.next
            current_other = current_other.next
        return current_self is None and current_other is None
    
    def to_list(self):
        """Convert ListNode to list for easier testing"""
        result = []
        current = self
        while current:
            result.append(current.val)
            current = current.next
        return result

class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right
    
    def __eq__(self, other):
        if not isinstance(other, TreeNode):
            return False
        if self.val != other.val:
            return False
        return (self.left == other.left) and (self.right == other.right)

class Solution:
    def processListNode(self, head):
        """Simple function that returns the input ListNode"""
        return head
    
    def processTreeNode(self, root):
        """Simple function that returns the input TreeNode"""
        return root
    
    def mergeListNodes(self, head1, head2):
        """Merge two linked lists by appending second to first"""
        if not head1:
            return head2
        if not head2:
            return head1
        
        current = head1
        while current.next:
            current = current.next
        current.next = head2
        return head1
    
    def addToListNode(self, head, val):
        """Add a value to the end of a linked list"""
        if not head:
            return ListNode(val)
        
        current = head
        while current.next:
            current = current.next
        current.next = ListNode(val)
        return head
