[{"description": "Reverse linked list - simple case", "input": [{"ListNode": [1, 2, 3]}], "expected": {"ListNode": [3, 2, 1]}}, {"description": "Reverse linked list - single node", "input": [{"ListNode": [1]}], "expected": {"ListNode": [1]}}, {"description": "Invert binary tree - simple case", "input": [{"TreeNode": [1, 2, 3]}], "expected": {"TreeNode": [1, 3, 2]}}, {"description": "Invert binary tree - single node", "input": [{"TreeNode": [1]}], "expected": {"TreeNode": [1]}}]