[{"description": "Single-key dict with registered deserializer", "input": [{"ListNode": [1, 2, 3]}], "expected": {"ListNode": [3, 2, 1]}}, {"description": "Single-key dict with unregistered key (treated as regular dict)", "input": [{"CustomClass": [1, 2, 3]}], "expected": {"result": "processed"}}, {"description": "Multi-key regular dictionary (always treated as regular dict)", "input": [{"key": "value", "count": 5}], "expected": {"processed": true}}, {"description": "Mixed nested structure", "input": [{"nodes": [{"ListNode": [1, 2]}, {"ListNode": [3, 4]}], "metadata": {"count": 2, "type": "linked_list"}}], "expected": {"ListNode": [1, 2, 3, 4]}}, {"description": "List with mixed dictionary types", "input": [[{"ListNode": [1, 2, 3]}, {"key": "value"}, {"TreeNode": [5]}]], "expected": {"processed": "mixed_types"}}]