[{"description": "Reverse linked list - simple case", "input": [{"ListNode": [1, 2, 3, 4, 5]}], "expected": {"ListNode": [5, 4, 3, 2, 1]}}, {"description": "Reverse linked list - single node", "input": [{"ListNode": [1]}], "expected": {"ListNode": [1]}}, {"description": "Reverse linked list - empty list", "input": [{"ListNode": []}], "expected": null}, {"description": "Invert binary tree - simple case", "input": [{"TreeNode": [4, 2, 7, 1, 3, 6, 9]}], "expected": {"TreeNode": [4, 7, 2, 9, 6, 3, 1]}}, {"description": "Invert binary tree - single node", "input": [{"TreeNode": [1]}], "expected": {"TreeNode": [1]}}, {"description": "Invert binary tree - empty tree", "input": [{"TreeNode": []}], "expected": null}, {"description": "Merge two sorted lists", "input": [{"ListNode": [1, 2, 4]}, {"ListNode": [1, 3, 4]}], "expected": {"ListNode": [1, 1, 2, 3, 4, 4]}}, {"description": "Merge two sorted lists - one empty", "input": [{"ListNode": []}, {"ListNode": [0]}], "expected": {"ListNode": [0]}}, {"description": "Maximum depth of binary tree", "input": [{"TreeNode": [3, 9, 20, null, null, 15, 7]}], "expected": 3}, {"description": "Maximum depth - single node", "input": [{"TreeNode": [1]}], "expected": 1}]