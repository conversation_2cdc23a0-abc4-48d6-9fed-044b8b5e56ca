from setuptools import setup, find_packages

setup(
    name="pyleet",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[],
    entry_points={
        "console_scripts": [
            "pyleet = pyleet.__main__:main"
        ]
    },
    author="ergs0204",
    author_email="<EMAIL>",
    description="Run LeetCode Python solutions locally with test cases",
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.7',
)
